import React, { useState, useRef, useEffect } from "react";
import {
  OptimizedMarketIndex,
  OptimizedLeftNav,
  OptimizedRightNav,
  OptimizedErrorContainer,
  OptimizedTopNav,
} from "../components/Layout/OptimizedComponenets";
import { FaPlus, FaEdit, FaTrash } from "react-icons/fa";
import { useSelector } from "react-redux";
import useClickOutside from "../hooks/useClickOutside";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import TableHeaderWithFilter from "../components/TableHeaderWithFilter";
import filterIcon from "../assets/newFilter.png";


const styles = `
  .middle-main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .equity-table-container {
    overflow: auto;
    height: calc(70vh - 100px);
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  }

  .equity-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
  }

  .equity-table th {
    background-color: #D8E1FF;
    padding: 4px 3px;
    border-bottom: 1px solid #ddd;
    border-right: 1px solid #ddd;
    white-space: normal;
    font-size: 15px;
    vertical-align: middle;
    height: auto;
    line-height: 1.1;
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    min-width: 70px; /* Increased minimum width for better appearance */
    word-break: break-word;
    hyphens: auto;
    text-align: center;
  }

  .equity-table th span {
    font-weight: 600;
    font-size: 14px;
  }

  .equity-table td {
    padding: 4px;
    border-bottom: 1px solid #ddd;
    border-right: 1px solid #ddd;
    text-align: center;
    vertical-align: middle;
  }

  .equity-table tbody tr:nth-child(even),
  .equity-table tbody tr:nth-child(even) input,
  .equity-table tbody tr:nth-child(even) select {
    background-color: #E8E6E6;
  }

  .equity-table tbody tr:nth-child(odd),
  .equity-table tbody tr:nth-child(odd) input,
  .equity-table tbody tr:nth-child(odd) select {
    background-color: #FFFFFF;
  }

  .equity-table tr:hover {
    background-color: #f0f0f0;
  }

  .action-cell {
    display: flex;
    gap: 10px;
    justify-content: center;
  }

  .action-icon {
    cursor: pointer;
    color: #555;
  }

  .action-icon:hover {
    color: #000;
  }

  .checkbox-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .filter-icon {
    margin-left: 2px;
    cursor: pointer;
    font-size: 16px;
    vertical-align: middle;
  }

  .tooltip-container {
    position: relative;
    display: inline-block;
    margin: 0 2px;
  }
`;

function Equity() {
  const { collapsed } = useSelector((state) => state.collapseReducer);
  const [msgs, setMsgs] = useState([]);
  const [symbols, setSymbols] = useState([

  ]);

  // Column configuration for table headers
  const columns = [
    { key: 'enabled', label: 'Enabled', minWidth: '130px', useTableHeaderWithFilter: true },
    { key: 'edit', label: 'Edit', minWidth: '60px', hasFilter: false },
    { key: 'delete', label: 'Delete', minWidth: '60px', hasFilter: false },
    { key: 'sourceSymbol', label: 'Source Symbol', minWidth: '100px', useTableHeaderWithFilter: true },
    { key: 'dataProvider', label: 'Data Provider', minWidth: '100px', hasFilter: true },
    { key: 'mapping', label: 'Mapping', minWidth: '80px', hasFilter: true },
    { key: 'exchange', label: 'Exchange', minWidth: '80px', hasFilter: true },
    { key: 'exchangeSymbol', label: 'Exchange Symbol', minWidth: '80px', hasFilter: true },
    { key: 'product', label: 'Product', minWidth: '80px', hasFilter: true },
    { key: 'entryOrder', label: 'Entry Order', minWidth: '80px', hasFilter: true },
    { key: 'exitOrder', label: 'Exit Order', minWidth: '80px', hasFilter: true },
    { key: 'strategies', label: 'Strategies', minWidth: '80px', hasFilter: true },
    { key: 'qtyType', label: 'Qty Type', minWidth: '80px', hasFilter: true },
    { key: 'qtyValue', label: 'Qty Value', minWidth: '80px', hasFilter: true },
    { key: 'maxQty', label: 'Max Qty', minWidth: '80px', hasFilter: true },
    { key: 'maxOpenPos', label: 'Max Open Pos', minWidth: '80px', hasFilter: true },
    { key: 'maxOpenTrades', label: 'Max Open Trades', minWidth: '80px', hasFilter: true },
    { key: 'maxTrades', label: 'Max Trades', minWidth: '80px', hasFilter: true },
    { key: 'maxProfitPerTrade', label: 'Max Profit per Trade', minWidth: '120px', hasFilter: true },
    { key: 'maxLossPerTrade', label: 'Max Loss Per Trade', minWidth: '120px', hasFilter: true },
    { key: 'maxSignalPerMinute', label: 'Max Signal Per Minute', minWidth: '120px', hasFilter: true },
    { key: 'noSignalForSeconds', label: 'No Signal For Seconds', minWidth: '120px', hasFilter: true },
    { key: 'stopLoss', label: 'Stop Loss', minWidth: '80px', hasFilter: true },
    { key: 'slTrailing', label: 'SL Trailing', minWidth: '80px', hasFilter: true },
    { key: 'target', label: 'Target', minWidth: '80px', hasFilter: true },
    { key: 'tgtTrailing', label: 'Tgt Trailing', minWidth: '80px', hasFilter: true },
    { key: 'breakEven', label: 'Break Even', minWidth: '80px', hasFilter: true },
    { key: 'maxLtpDiff', label: 'Max LTP Diff', minWidth: '100px', hasFilter: true },
    { key: 'priceSpread', label: 'Price Spread', minWidth: '100px', hasFilter: true },
    { key: 'cancelIfNotComplete', label: 'Cancel If Not Complete', minWidth: '140px', hasFilter: true }
  ];
  // Filtering state
  const [filters, setFilters] = useState({});
  const [filterPopup, setFilterPopup] = useState(null);
  const [tempFilters, setTempFilters] = useState({});
  const [filteredSymbols, setFilteredSymbols] = useState([]);
  const [popupPosition, setPopupPosition] = useState({ top: 0, left: 0 });
  const filterPopupRef = useRef(null);
  const errorContainerRef = useRef(null);

  // Use click outside hook to close filter popup
  useClickOutside(filterPopupRef, () => setFilterPopup(null));

  // Apply filters when symbols or filters change
  useEffect(() => {
    if (Object.keys(filters).length > 0) {
      const filteredData = symbols.filter((row) =>
        Object.keys(filters).every((col) =>
          filters[col]?.length > 0 ? filters[col].includes(String(row[col])) : true
        )
      );
      setFilteredSymbols(filteredData);
    } else {
      setFilteredSymbols(symbols);
    }
  }, [symbols, filters]);

  const handleClearLogs = () => {
    if (msgs.length === 0) return;
    setMsgs([]);
  };

  const handleDeleteRow = (id) => {
    setSymbols(symbols.filter(symbol => symbol.id !== id));
  };

  const handleToggleEnabled = (id) => {
    setSymbols(symbols.map(symbol =>
      symbol.id === id ? { ...symbol, enabled: !symbol.enabled } : symbol
    ));
  };

  // Filter functions
  const handleFilterToggle = (column, event) => {
    // Add safety check for event and event.currentTarget
    if (!event || !event.currentTarget) {
      console.warn('handleFilterToggle called without valid event or event.currentTarget');
      return;
    }

    try {
      const { top, left, height } = event.currentTarget.getBoundingClientRect();
      setFilterPopup(filterPopup === column ? null : column);
      setPopupPosition({ top: top + height, left });
      setTempFilters({ ...filters });
    } catch (error) {
      console.error('Error in handleFilterToggle:', error);
      // Fallback: still toggle the filter popup but without positioning
      setFilterPopup(filterPopup === column ? null : column);
      setTempFilters({ ...filters });
    }
  };

  const getDynamicUniqueValues = (column) => {
    return Array.from(new Set(symbols.map((row) => String(row[column]))));
  };

  const handleFilterChange = (column, value) => {
    setTempFilters((prev) => {
      const columnFilters = prev[column] || [];
      if (columnFilters.includes(value)) {
        return { ...prev, [column]: columnFilters.filter((v) => v !== value) };
      } else {
        return { ...prev, [column]: [...columnFilters, value] };
      }
    });
  };

  const handleSelectAll = (column) => {
    const currentOptions = getDynamicUniqueValues(column);
    const selectedOptions = tempFilters[column] || [];
    const allSelected = currentOptions.every((opt) => selectedOptions.includes(opt));

    if (allSelected) {
      setTempFilters((prev) => ({ ...prev, [column]: [] }));
    } else {
      setTempFilters((prev) => ({ ...prev, [column]: [...currentOptions] }));
    }
  };

  const handleApplyFilter = () => {
    const newFilters = { ...filters, [filterPopup]: tempFilters[filterPopup] || [] };
    setFilters(newFilters);
    setFilterPopup(null);
  };

  const handleCancelFilter = () => {
    setTempFilters(filters);
    setFilterPopup(null);
  };

  // Helper function to render table headers
  const renderTableHeader = (column) => {
    const { key, label, minWidth, hasFilter, useTableHeaderWithFilter } = column;

    const baseStyle = {
      fontSize: "13px",
      padding: "4px 3px",
      textAlign: "center",
      backgroundColor: filters[key]?.length > 0 ? "#f0f7ff" : "#D8E1FF",
      borderBottom: filters[key]?.length > 0 ? "2px solid #1976d2" : "inherit",
      height: "auto",
      minWidth: minWidth
    };

    const containerStyle = {
      display: "flex",
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      position: "relative",
      padding: "0",
      margin: "0",
      gap: "4px",
      width: "100%"
    };

    if (useTableHeaderWithFilter) {
      return (
        <th key={key} style={baseStyle}>
          <div style={containerStyle}>
            <TableHeaderWithFilter
              col={label}
              columnDisplayNames={{}}
              hasFilter={filters[key]?.length > 0}
              selectedItems={filters[key]?.length || 0}
              handleFilterToggle={handleFilterToggle}
              filterIcon={filterIcon}
            />
          </div>
        </th>
      );
    }

    if (!hasFilter) {
      return (
        <th key={key} style={baseStyle}>
          <div style={containerStyle}>
            <span style={{ fontWeight: "600", fontSize: "12px" }}>{label}</span>
          </div>
        </th>
      );
    }

    return (
      <th key={key} style={baseStyle}>
        <div style={containerStyle}>
          <span style={{ fontWeight: "600", fontSize: "12px" }}>{label}</span>
          <span
            onClick={(e) => handleFilterToggle(key, e)}
            style={{
              cursor: "pointer",
              fontSize: "14px",
              color: filters[key] && filters[key].length > 0 ? "#1976d2" : "black"
            }}
          >
            <PlayArrowIcon
              style={{
                transform: "rotate(90deg)",
                fontSize: "14px",
                color: filters[key] && filters[key].length > 0 ? "#1976d2" : "black"
              }}
            />
          </span>
        </div>
      </th>
    );
  };


  return (
    <div>
      <style>{styles}</style>
      <OptimizedMarketIndex />
      <div className="main-section">
        <OptimizedLeftNav />
        <div className="middle-main-container">
          <OptimizedTopNav />
          <div className="equity-table-container">
            <div style={{ overflowX: "auto" }}>
              <table className="equity-table">
                <thead style={{ position: "sticky", top: 0, zIndex: "10", backgroundColor: "#D8E1FF" }}>
                  <tr>
                    {columns.map(column => renderTableHeader(column))}
                  </tr>
                </thead>
                <tbody>
                  {filteredSymbols.map((symbol) => (
                    <tr key={symbol.id}>
                      <td className="checkbox-center">
                        <FaPlus className="action-icon"
                          style={{ color: "green" }}
                        />
                        <input
                          type="checkbox"
                          checked={symbol.enabled}
                          onChange={() => handleToggleEnabled(symbol.id)}
                          style={{ transform: "scale(1.5)", margin: "10px", }}
                        />
                      </td>
                      <td className="edit-center">
                        <FaEdit className="action-icon"
                          style={{ color: "green", transform: "scale(1.5)", margin: "5px", }}
                        />
                      </td>
                      <td className="delete-center">
                        <FaTrash
                          className="action-icon"
                          style={{ color: "red" }}
                          onClick={() => handleDeleteRow(symbol.id)}
                        />
                      </td>
                      <td>{symbol.sourceSymbol}</td>
                      <td>{symbol.dataProvider}</td>
                      <td>{symbol.mapping}</td>
                      <td>{symbol.exchange}</td>
                      <td>{symbol.exchangeSymbol}</td>
                      <td>{symbol.product}</td>
                      <td>{symbol.entryOrder}</td>
                      <td>{symbol.exitOrder}</td>
                      <td>{symbol.strategies}</td>
                      <td>{symbol.qtyType}</td>
                      <td>{symbol.qtyValue}</td>
                      <td>{symbol.maxQty}</td>
                      <td>{symbol.maxOpenPos}</td>
                      <td>{symbol.maxOpenTrades}</td>
                      <td>{symbol.maxTrades}</td>
                      <td>{symbol.maxProfitPerTrade}</td>
                      <td>{symbol.maxLossPerTrade}</td>
                      <td>{symbol.maxSignalPerMinute}</td>
                      <td>{symbol.noSignalForSeconds}</td>
                      <td>{symbol.stopLoss.toFixed(2)}</td>
                      <td>{symbol.slTrailing.toFixed(2)}</td>
                      <td>{symbol.target.toFixed(2)}</td>
                      <td>{symbol.tgtTrailing.toFixed(2)}</td>
                      <td>{symbol.breakEven.toFixed(2)}</td>
                      <td>{symbol.maxLtpDiff.toFixed(2)}</td>
                      <td>{symbol.priceSpread.toFixed(2)}</td>
                      <td>{symbol.cancelIfNotComplete}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          <div className="add_collapse">
            <button
              style={{ zIndex: "0", marginLeft: "90%" }}
              onClick={() => {
                errorContainerRef.current.toggleCollapse();
              }}
              className="button"
              id="collapse"
            >
              {collapsed ? "Expand" : "Collapse"}
            </button>
          </div>

          <OptimizedErrorContainer msgs={msgs} ref={errorContainerRef} handleClearLogs={handleClearLogs} />
        </div>
        <OptimizedRightNav />
      </div>

      {/* Filter Popup */}
      {filterPopup && (
        <div
          ref={filterPopupRef}
          style={{
            position: "absolute",
            top: `${popupPosition.top + 5}px`,
            left: `${popupPosition.left}px`,
            background: "#ffffff",
            border: "1px solid #e0e0e0",
            borderRadius: "8px",
            padding: "3px 0 3px 3px",
            boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
            zIndex: 1000,
            minWidth: "150px",
            maxWidth: "200px"
          }}
        >
          <div style={{ paddingRight: "1px" }}>
            <div style={{
              borderBottom: "1px solid #e0e0e0",
              paddingBottom: "1px",
              marginBottom: "1px",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center"
            }}>
              <span style={{ fontWeight: "bold", color: "#1976d2" }}>
                Filter: {filterPopup}
              </span>
              <span style={{
                fontSize: "12px",
                color: "#666",
                backgroundColor: "#f5f5f5",
                padding: "2px 6px",
                borderRadius: "4px"
              }}>
                {getDynamicUniqueValues(filterPopup).length} items
              </span>
            </div>

            <label
              style={{
                display: "flex",
                alignItems: "center",
                padding: "1px 1px",
                cursor: "pointer",
                fontWeight: "500",
                color: "#333",
                marginBottom: "1px",
                backgroundColor: "#f8f9fa",
                borderRadius: "4px",
                marginRight: "1px"
              }}
            >
              <input
                type="checkbox"
                checked={
                  tempFilters[filterPopup] &&
                  getDynamicUniqueValues(filterPopup).every((opt) =>
                    tempFilters[filterPopup].includes(opt)
                  )
                }
                onChange={() => handleSelectAll(filterPopup)}
                style={{ marginRight: "8px" }}
              />
              <span>Select All</span>
            </label>

            <div
              style={{
                maxHeight: "150px",
                overflowY: "auto",
                margin: "0",
                scrollbarWidth: "thin",
                scrollbarColor: "#888 #f1f1f1",
                border: "1px solid #eee",
                borderRadius: "4px",
                marginRight: "1px"
              }}
            >
              {getDynamicUniqueValues(filterPopup).length > 0 ? (
                getDynamicUniqueValues(filterPopup).map((item) => {
                  const isSelected = tempFilters[filterPopup]?.includes(item) || false;
                  return (
                    <div
                      key={item}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        padding: "1px 2px",
                        cursor: "pointer",
                        margin: "0",
                        borderBottom: "1px solid #f0f0f0",
                        backgroundColor: isSelected ? "#f0f7ff" : "transparent",
                        transition: "background-color 0.2s"
                      }}
                      onClick={() => handleFilterChange(filterPopup, item)}
                    >
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => { }}
                        style={{ marginRight: "8px" }}
                      />
                      <span style={{
                        color: isSelected ? "#1976d2" : "#444",
                        fontWeight: isSelected ? "500" : "normal"
                      }}>
                        {item || "(Empty)"}
                      </span>
                    </div>
                  );
                })
              ) : (
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    padding: "10px",
                    color: "#666",
                    fontStyle: "italic",
                    minHeight: "40px",
                    justifyContent: "center"
                  }}
                >
                  <span>No options available</span>
                </div>
              )}
            </div>

            <div style={{
              fontSize: "12px",
              color: "#666",
              margin: "1px 0",
              display: "flex",
              justifyContent: "space-between",
              marginRight: "1px"
            }}>
              <span>
                {tempFilters[filterPopup]?.length || 0} of {getDynamicUniqueValues(filterPopup).length} selected
              </span>
              {tempFilters[filterPopup]?.length > 0 && (
                <span
                  style={{ color: "#1976d2", cursor: "pointer" }}
                  onClick={() => setTempFilters({ ...tempFilters, [filterPopup]: [] })}
                >
                  Clear selection
                </span>
              )}
            </div>

            <div style={{
              display: "flex",
              justifyContent: "flex-end",
              marginTop: "5px",
              gap: "5px",
              marginRight: "1px"
            }}>
              <button
                onClick={handleCancelFilter}
                style={{
                  padding: "3px 8px",
                  border: "1px solid #ddd",
                  borderRadius: "4px",
                  backgroundColor: "#f5f5f5",
                  cursor: "pointer",
                  fontSize: "12px"
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleApplyFilter}
                style={{
                  padding: "3px 8px",
                  border: "none",
                  borderRadius: "4px",
                  backgroundColor: "#1976d2",
                  color: "white",
                  cursor: "pointer",
                  fontSize: "12px"
                }}
              >
                Apply
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Equity;
